# Fixing 499 Antivirus Error

## Problem
The error "Failed to load resource: the server responded with a status of 499 (Request has been forbidden by antivirus)" occurs when antivirus software blocks network requests, particularly to external services like Google Maps.

## Solutions Applied

### 1. Content Security Policy (CSP)
Added CSP headers to `index.html` to explicitly allow connections to Google services:
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://www.google.com https://maps.google.com https://maps.googleapis.com; frame-src 'self' https://www.google.com https://maps.google.com;">
```

### 2. Updated Google Maps URL Format
Changed from the API-style URL to a more standard format:
- **Before**: `https://www.google.com/maps/search/?api=1&query=...`
- **After**: `https://maps.google.com/maps?q=...&hl=en`

### 3. Error Handling with Fallback
Added JavaScript error handling that:
- Tries to open the maps link normally
- If blocked, copies the address to clipboard
- Shows a user-friendly message

### 4. Vite Configuration
Updated `vite.config.ts` with headers to handle cross-origin policies:
```typescript
server: {
  headers: {
    'Cross-Origin-Embedder-Policy': 'unsafe-none',
    'Cross-Origin-Opener-Policy': 'same-origin-allow-popups',
  },
}
```

## Additional Troubleshooting

### If the error persists:

1. **Whitelist the application in your antivirus**:
   - Add `localhost:5174` to your antivirus whitelist
   - Add `*.google.com` and `*.googleapis.com` to allowed domains

2. **Temporarily disable real-time protection**:
   - Test if the issue resolves with antivirus temporarily disabled
   - If so, add permanent exceptions

3. **Browser-specific fixes**:
   - Clear browser cache and cookies
   - Disable browser security extensions temporarily
   - Try in incognito/private mode

4. **Alternative map services**:
   - Consider using OpenStreetMap or other mapping services
   - Implement a simple address display without external links

## Testing
1. Start the development server: `npm run dev`
2. Open http://localhost:5174/
3. Select a donor and try clicking "View on Google Maps"
4. The link should now work or provide a helpful fallback

## Production Deployment
When deploying to production, ensure your hosting provider supports:
- Custom headers (for CSP)
- HTTPS (required for clipboard API)
- Proper CORS configuration
