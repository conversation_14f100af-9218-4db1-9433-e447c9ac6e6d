import React from 'react';
import { ChevronDown } from 'lucide-react';
import { Donor } from '../types/donor';

interface DonorSelectorProps {
  donors: Don<PERSON>[];
  selectedDonor: Donor | null;
  onSelectDonor: (donor: Donor | null) => void;
}

export default function DonorSelector({ donors, selectedDonor, onSelectDonor }: DonorSelectorProps) {
  return (
    <div className="relative">
      <label htmlFor="donor-select" className="block text-sm font-medium text-gray-700 mb-2">
        Select a Donor
      </label>
      <div className="relative">
        <select
          id="donor-select"
          className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500 appearance-none text-gray-900 font-medium cursor-pointer transition-all duration-200"
          value={selectedDonor?.id || ''}
          onChange={(e) => {
            const donor = donors.find(d => d.id === e.target.value) || null;
            onSelectDonor(donor);
          }}
        >
          <option value="">Choose a donor...</option>
          {donors.map((donor) => (
            <option key={donor.id} value={donor.id}>
              {donor.name} {donor.organization ? `- ${donor.organization}` : ''}
            </option>
          ))}
        </select>
        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
      </div>
    </div>
  );
}