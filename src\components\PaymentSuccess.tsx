import React from 'react';
import { CheckCircle, Heart, ArrowLeft } from 'lucide-react';
import { Donor } from '../types/donor';
import { DonationTransaction } from '../types/donation';

interface PaymentSuccessProps {
  donor: Donor;
  transaction: DonationTransaction;
  onBackToDonors: () => void;
}

export default function PaymentSuccess({ donor, transaction, onBackToDonors }: PaymentSuccessProps) {
  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 animate-fadeIn">
      {/* Success Header */}
      <div className="bg-gradient-to-r from-green-500 to-green-600 px-6 py-8 text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-white bg-opacity-20 p-3 rounded-full">
            <CheckCircle className="h-12 w-12 text-white" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Payment Successful!</h2>
        <p className="text-green-100">Thank you for your generous donation</p>
      </div>

      <div className="p-6">
        {/* Transaction Details */}
        <div className="bg-gray-50 rounded-xl p-4 mb-6">
          <h3 className="font-semibold text-gray-900 mb-3">Transaction Details</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Transaction ID:</span>
              <span className="font-mono text-gray-900">{transaction.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="font-semibold text-gray-900">${transaction.amount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Recipient:</span>
              <span className="text-gray-900">{donor.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Date:</span>
              <span className="text-gray-900">{transaction.timestamp.toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Type:</span>
              <span className="text-gray-900 capitalize">{transaction.donationType}</span>
            </div>
          </div>
        </div>

        {/* Impact Message */}
        <div className="bg-pink-50 border border-pink-200 rounded-xl p-4 mb-6">
          <div className="flex items-start space-x-3">
            <Heart className="h-5 w-5 text-pink-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-pink-900 mb-1">Your Impact</h4>
              <p className="text-pink-800 text-sm">
                Your donation will help {donor.organization || donor.name} continue their important work in the community. 
                You'll receive a receipt via email shortly.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={onBackToDonors}
            className="w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-4 rounded-xl font-semibold hover:from-pink-600 hover:to-rose-600 transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Donors</span>
          </button>
          
          <button
            onClick={() => window.print()}
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200 transition-all duration-200"
          >
            Print Receipt
          </button>
        </div>
      </div>
    </div>
  );
}