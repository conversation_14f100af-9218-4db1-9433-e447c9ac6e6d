import { <PERSON><PERSON> } from '../types/donor';

export const donors: <PERSON><PERSON>[] = [
  {
    id: '1',
    name: '<PERSON>',
    organization: 'Community Care Foundation',
    address: '123 Charity Lane',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'monetary',
    qrData: 'donation://sarah-johnson/community-care-foundation?amount=100&type=monetary'
  },
  {
    id: '2',
    name: '<PERSON>',
    organization: 'Food Bank Alliance',
    address: '456 Helping Hand Drive',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90210',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'food',
    qrData: 'donation://michael-chen/food-bank-alliance?type=food&location=downtown'
  },
  {
    id: '3',
    name: '<PERSON>',
    organization: 'Warm Hearts Clothing Drive',
    address: '789 Compassion Street',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'clothing',
    qrData: 'donation://emily-rodriguez/warm-hearts?type=clothing&season=winter'
  },
  {
    id: '4',
    name: 'Dr. David Kim',
    organization: 'Medical Aid Society',
    address: '321 Healthcare Boulevard',
    city: 'Chicago',
    state: 'IL',
    zipCode: '60601',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'medical',
    qrData: 'donation://david-kim/medical-aid-society?type=medical&priority=urgent'
  },
  {
    id: '5',
    name: 'Lisa Thompson',
    organization: 'Hope & Help Center',
    address: '654 Kindness Avenue',
    city: 'Seattle',
    state: 'WA',
    zipCode: '98101',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'general',
    qrData: 'donation://lisa-thompson/hope-help-center?type=general&urgent=true'
  },
  {
    id: '6',
    name: 'Robert Martinez',
    organization: 'Youth Education Fund',
    address: '987 Learning Lane',
    city: 'Austin',
    state: 'TX',
    zipCode: '73301',
    phone: '(*************',
    email: '<EMAIL>',
    donationType: 'monetary',
    qrData: 'donation://robert-martinez/youth-education-fund?amount=250&type=education'
  }
];